# SOC Automation Suite

A comprehensive security automation toolkit that integrates network reconnaissance, file integrity monitoring, threat intelligence enrichment, and automated reporting.

## Features

- **Network Scanning**: Automated network reconnaissance and port scanning
- **File Integrity Monitoring**: Real-time and scheduled file integrity checks
- **Threat Intelligence**: IP and domain reputation checks using multiple sources
- **Automated Reporting**: Generate comprehensive reports in JSON and HTML formats
- **Task Scheduling**: Automated execution of security tasks
- **Real-time Monitoring**: Continuous file system monitoring with alerts

## Installation

### Prerequisites

- Python 3.8 or higher
- pip package manager

### Install Dependencies

```bash
pip install -r requirements.txt
```

### Install as Package

```bash
pip install -e .
```

## Quick Start

### Demo <PERSON>ript

Run the demo script to see all features in action:

```bash
python demo.py
```

### Basic Usage

```bash
# Scan a single target
python main.py --scan *******

# Check threat intelligence for an IP
python main.py --threat-intel *******

# Create file integrity baseline
python main.py --create-baseline /path/to/monitor

# Check files against baseline
python main.py --check-baseline

# Start continuous monitoring
python main.py --monitor
```

### API Usage

```python
from soc_automation import SOCAutomationSuite

# Initialize the suite
suite = SOCAutomationSuite()

# Perform network scan
result = suite.scanner.comprehensive_scan("*******")
print(result)

# Check threat intelligence
threat_data = suite.threat_intel.check_ip_reputation("*******")
print(threat_data)

# Create file integrity baseline
suite.file_monitor.create_baseline("/path/to/monitor")

# Start continuous monitoring
suite.start_monitoring()
```

## Configuration

### Environment Variables

Set the following environment variables for enhanced functionality:

```bash
export VT_API_KEY="your_virustotal_api_key"
export ABUSE_IP_DB_KEY="your_abuse_ip_db_api_key"
```

### File Monitoring

By default, the file monitor watches `/var/www` on Linux/Mac and `C:\inetpub\wwwroot` on Windows. You can specify a different path when creating the baseline.

## Project Structure

```text
soc_automation_suite/
├── soc_automation/
│   ├── __init__.py          # Package initialization
│   ├── scanner.py           # NetworkScanner class
│   ├── threat_intel.py      # ThreatIntelligence class
│   ├── file_monitor.py      # FileIntegrityMonitor class
│   ├── reporter.py          # ReportGenerator class
│   └── suite.py             # SOCAutomationSuite orchestrator
├── main.py                  # CLI entry point
├── requirements.txt         # Dependencies
├── setup.py                 # Package setup
├── README.md                # This file
├── reports/                 # Generated reports directory
└── soc_automation.log       # Log file
```

## Modules

### NetworkScanner

- Ping checks for host reachability
- Multi-threaded port scanning
- Service banner grabbing
- Network Mapper (Nmap) integration (if available)

### ThreatIntelligence

- IP reputation checks via IPInfo.io
- Abuse IPDB integration (with API key)
- Domain registration lookups

### FileIntegrityMonitor

- SHA256 hash-based file integrity checking
- Real-time file system monitoring
- Baseline creation and comparison
- Suspicious file type alerts

### ReportGenerator

- JSON and HTML report generation
- Comprehensive scan summaries
- Customizable report templates

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Security Notice

This tool is intended for authorized security testing and monitoring only. Users are responsible for ensuring they have proper authorization before scanning or monitoring any systems.

## Support

For issues and questions:

- Create an issue on GitHub
- Check the documentation wiki
- Contact the security team
